import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Input, Switch, Button, Form, Avatar, Divider } from 'antd';
import {UserOutlined } from '@ant-design/icons';
import { PreloadImage } from '@/components/Image';
import FileSelector from '@/components/FATWall_PC/FileSelector';
import del_icon_2 from '@/Resources/icon/del_icon.png';
import UserSelector from '@/components/FATWall_PC/UserSelector';
import del_icon from '@/Resources/icon/del.png';
import { Toast } from '@/components/Toast/manager';
import request from '@/request';
import add_icon from '@/Resources/icon/add.png';
import close from '@/Resources/icon/close.png';
import close_white from '@/Resources/icon/close_white.png';
import styles from './index.module.scss';
import { useTheme } from '@/utils/themeDetector';

interface User {
    id: string;
    name: string;
    position: string;
    avatar?: string;
}

interface SourceItem {
    path: string;
    displayPath: string;
}

interface FormValues {
    libraryName: string;
    showOnTV: boolean;
}

interface EditLibraryData {
    lib_id: number;
    name: string;
    tv_visable: number;
    scan_path: string[];
    share2who_list: (string | number)[];
}

interface CreateLibraryModalProps {
    visible: boolean;
    onClose: () => void;
    onSuccess: () => void;
    libraryCount: number; // 当前媒体库数量，用于生成默认名称
    // 编辑相关props
    isEditMode?: boolean;
    editData?: EditLibraryData;
}

const CreateLibraryModal: React.FC<CreateLibraryModalProps> = ({
    visible,
    onClose,
    onSuccess,
    libraryCount,
    isEditMode = false,
    editData
}) => {
    const [isFileSelectorOpen, setIsFileSelectorOpen] = useState(false);
    const [isUserSelectorOpen, setIsUserSelectorOpen] = useState(false);
    const [selectedSources, setSelectedSources] = useState<SourceItem[]>([]);
    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
    const [form] = Form.useForm<FormValues>();
    const [hasUserInput, setHasUserInput] = useState(false); // 记录用户是否已经输入过
    const [loading, setLoading] = useState(false); // 手动控制loading状态
    const [originalEditData, setOriginalEditData] = useState<EditLibraryData | null>(null); // 保存原始编辑数据
    const [libraryName, setLibraryName] = useState(''); // 监听媒体库名称变化
    const { isDarkMode } = useTheme() as any;

    // 计算是否可以提交：媒体库名称不为空且至少有一个来源
    const canSubmit = libraryName.trim().length > 0 && selectedSources.length > 0;

    // 重置表单状态的函数
    const resetFormState = useCallback(() => {
        setTimeout(() => {
            setSelectedSources([]);
            setSelectedUsers([]);
            setHasUserInput(false); // 重置用户输入状态
            setLibraryName(''); // 重置媒体库名称状态
            form.resetFields();
        }, 100);
    }, [form]);

    // 当弹窗打开时初始化表单
    useEffect(() => {
        if (visible) {
            setHasUserInput(isEditMode); // 编辑模式下标记为已有输入
            // 延迟重置表单以避免竞态条件
            setTimeout(() => {
                if (isEditMode && editData) {
                    // 编辑模式：使用编辑数据初始化
                    setOriginalEditData(editData);
                    setLibraryName(editData.name);
                    form.setFieldsValue({
                        libraryName: editData.name,
                        showOnTV: editData.tv_visable === 1
                    });

                    // 设置来源路径
                    setSelectedSources(editData.scan_path.map(path => ({
                        path,
                        displayPath: path
                    })));

                    // 设置分享用户（模拟用户数据，实际项目中应该从API获取）
                    const mockUsers: User[] = editData.share2who_list.map((userId: string | number) => ({
                        id: String(userId),
                        name: `用户${userId}`,
                        position: '普通用户',
                        avatar: ''
                    }));
                    setSelectedUsers(mockUsers);
                } else {
                    // 创建模式：使用默认值
                    const defaultName = `媒体库${libraryCount + 1}`;
                    setOriginalEditData(null);
                    setLibraryName(defaultName);
                    form.resetFields();
                    form.setFieldsValue({
                        libraryName: defaultName,
                        showOnTV: false
                    });
                    setSelectedSources([]);
                    setSelectedUsers([]);
                }
            }, 0);
        }
    }, [visible, form, libraryCount, isEditMode, editData]);

    // 处理弹窗取消
    const handleCancel = useCallback(() => {
        onClose();
        resetFormState();
    }, [onClose, resetFormState]);

    // 表单验证
    const validateForm = useCallback((): boolean => {
        const libraryName = form.getFieldValue('libraryName');
        const finalLibraryName = libraryName?.trim() || `媒体库${libraryCount + 1}`;

        if (!finalLibraryName.trim()) {
            Toast.show('请输入媒体库名称', { duration: 2000 });
            return false;
        }

        if (selectedSources.length === 0) {
            Toast.show('请至少添加一个媒体库来源', { duration: 2000 });
            return false;
        }

        return true;
    }, [form, selectedSources, libraryCount]);

    // 处理创建错误
    const handleCreateError = useCallback((code: number, message: string) => {
        let errorMsg = '';
        switch (code) {
            case 2200:
                errorMsg = '内部错误，请稍后重试';
                break;
            case 2201:
                errorMsg = '请求参数错误：选择的目录不存在或分享目标用户不存在';
                break;
            case 2202:
                errorMsg = '已超过媒体库创建上限';
                break;
            case 2204:
                errorMsg = '该名称的媒体库已存在，请使用其他名称';
                break;
            default:
                errorMsg = message || '创建失败，请重试';
        }

        Toast.show(errorMsg, { duration: 3000 });
    }, []);

    // 处理编辑错误
    const handleEditError = useCallback((code: number, message: string) => {
        let errorMsg = '';
        switch (code) {
            case 2200:
                errorMsg = '内部错误，请稍后重试';
                break;
            case 2201:
                errorMsg = '请求参数错误：选择的目录不存在或分享目标用户不存在';
                break;
            case 2203:
                errorMsg = '没有编辑该媒体库的权限';
                break;
            case 2204:
                errorMsg = '该名称的媒体库已存在，请使用其他名称';
                break;
            case 2205:
                errorMsg = '该媒体库不存在';
                break;
            default:
                errorMsg = message || '编辑失败，请重试';
        }

        Toast.show(errorMsg, { duration: 3000 });
    }, []);

    // 构建编辑参数（只包含有变化的字段）
    const buildEditParams = useCallback(() => {
        if (!originalEditData || !editData?.lib_id) {
            return null;
        }

        const currentFormValues = form.getFieldsValue();
        const params: any = {
            lib_id: editData.lib_id
        };

        // 检查名称是否有变化
        const currentName = currentFormValues.libraryName?.trim() || editData.name;
        if (currentName !== originalEditData.name) {
            params.name = currentName;
        }

        // 检查电视可见性是否有变化
        const currentTvVisible = currentFormValues.showOnTV ? 1 : 0;
        if (currentTvVisible !== originalEditData.tv_visable) {
            params.tv_visable = currentTvVisible;
        }

        // 检查扫描路径的变化
        const originalPaths = new Set(originalEditData.scan_path);
        const currentPaths = new Set(selectedSources.map(item => item.path));

        const pathsToAdd = selectedSources.map(item => item.path).filter(path => !originalPaths.has(path));
        const pathsToDelete = originalEditData.scan_path.filter(path => !currentPaths.has(path));

        if (pathsToAdd.length > 0) {
            params.path_add = pathsToAdd;
        }
        if (pathsToDelete.length > 0) {
            params.path_del = pathsToDelete;
        }

        // 检查分享用户的变化
        const currentUserIds = selectedUsers.map(user => String(user.id));
        const originalUserIds = (originalEditData.share2who_list || []).map(id => String(id));

        const originalUserSet = new Set(originalUserIds);
        const currentUserSet = new Set(currentUserIds);

        const usersToAdd = currentUserIds.filter(id => !originalUserSet.has(id));
        const usersToDelete = originalUserIds.filter(id => !currentUserSet.has(id));

        if (usersToAdd.length > 0) {
            params.share_add = usersToAdd;
        }
        if (usersToDelete.length > 0) {
            params.share_del = usersToDelete;
        }

        // 如果没有任何变化，返回null
        const hasChanges = params.name !== undefined ||
            params.tv_visable !== undefined ||
            params.path_add !== undefined ||
            params.path_del !== undefined ||
            params.share_add !== undefined ||
            params.share_del !== undefined;

        return hasChanges ? params : null;
    }, [originalEditData, editData, selectedSources, selectedUsers, form]);

    // 处理表单提交
    const handleSubmit = useCallback(async (values: FormValues) => {
        if (isEditMode) {
            // 编辑模式
            const editParams = buildEditParams();

            if (!editParams) {
                Toast.show('没有检测到任何变化', { duration: 2000 });
                return;
            }

            try {
                setLoading(true);
                console.log('编辑媒体库参数：', editParams);

                const response = await request.post('/mediacenter/lib_edit', editParams, {
                    showLoading: true
                });

                if (response.code === 0) {
                    Toast.show('编辑成功', { duration: 2000 });

                    // 关闭弹窗并重置表单
                    onClose();
                    resetFormState();

                    // 通知父组件刷新数据
                    onSuccess();
                } else {
                    handleEditError(response.code, response.result);
                }
            } catch (error) {
                console.error('编辑媒体库失败：', error);
                Toast.show('网络异常，请重试', { duration: 2000 });
            } finally {
                setLoading(false);
            }
        } else {
            // 创建模式
            // 表单验证
            if (!validateForm()) {
                return;
            }

            // 如果用户没有输入名称，使用默认名称
            const finalLibraryName = values.libraryName?.trim() || `媒体库${libraryCount + 1}`;

            try {
                setLoading(true);

                // 构建创建媒体库的参数
                const createParams = {
                    name: finalLibraryName,
                    tv_visable: values.showOnTV ? 1 : 0,
                    scan_path: selectedSources.map(item => item.path), // 扫描路径数组
                    share2who: selectedUsers.length > 0 ? selectedUsers.map(user => Number(user.id)) : undefined
                };

                console.log('创建媒体库参数：', createParams);

                const response = await request.post('/mediacenter/lib_create', createParams, {
                    showLoading: true
                });

                if (response.code === 0) {
                    Toast.show('创建成功', { duration: 2000 });

                    // 关闭弹窗并重置表单
                    onClose();
                    resetFormState();

                    // 通知父组件刷新数据
                    onSuccess();
                } else {
                    handleCreateError(response.code, response.result);
                }
            } catch (error) {
                console.error('创建媒体库失败：', error);
                Toast.show('网络异常，请重试', { duration: 2000 });
            } finally {
                setLoading(false);
            }
        }
    }, [isEditMode, buildEditParams, validateForm, selectedSources, selectedUsers, resetFormState, libraryCount, onClose, onSuccess, handleEditError, handleCreateError]);

    // 添加媒体库来源处理
    const handleAddSource = useCallback(() => {
        console.log('添加媒体库来源');
        setIsFileSelectorOpen(true);
    }, []);

    // 处理文件选择器选择
    const handleSourceSelect = useCallback((path: string, displayPath?: string) => {
        // 检查路径是否已存在
        if (selectedSources.some(item => item.path === path)) {
            Toast.show('该路径已添加', { duration: 1500 });
            return;
        }

        // 使用displayPath作为显示文本，如果没有则使用path
        const finalDisplayPath = displayPath || path;

        setSelectedSources(prev => [...prev, { path, displayPath: finalDisplayPath }]);
        setIsFileSelectorOpen(false);

        Toast.show(`已添加来源：${finalDisplayPath}`, { duration: 1500 });
    }, [selectedSources]);

    // 删除选中的来源
    const handleRemoveSource = useCallback((index: number) => {
        setSelectedSources(prev => prev.filter((_, i) => i !== index));
        Toast.show('已删除来源', { duration: 1000 });
    }, []);

    // 添加共享用户处理
    const handleAddUser = useCallback(() => {
        console.log('添加共享用户');
        setIsUserSelectorOpen(true);
    }, []);

    // 处理用户选择器确认
    const handleUserConfirm = useCallback((users: User[]) => {
        setSelectedUsers(users);
        Toast.show(`已添加${users.length}个共享用户`, { duration: 1500 });
    }, []);

    // 删除选中的用户
    const handleRemoveUser = useCallback((userId: string) => {
        setSelectedUsers(prev => prev.filter(user => user.id !== userId));
        Toast.show('已删除共享用户', { duration: 1000 });
    }, []);

    // 文件选择器关闭处理
    const handleFileSelectorClose = useCallback(() => {
        setIsFileSelectorOpen(false);
    }, []);

    // 用户选择器关闭处理
    const handleUserSelectorClose = useCallback(() => {
        setIsUserSelectorOpen(false);
    }, []);

    // 处理输入框点击事件（智能清空）
    const handleInputFocus = useCallback(() => {
        if (!hasUserInput) {
            // 首次点击时清空
            form.setFieldsValue({ libraryName: '' });
            setLibraryName('');
            setHasUserInput(true);
        }
    }, [hasUserInput, form]);

    // 处理输入框内容变化
    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        if (!hasUserInput) {
            setHasUserInput(true);
        }
        // 更新libraryName状态
        setLibraryName(e.target.value);
    }, [hasUserInput]);

    return (
        <>
            {/* 创建/编辑媒体库弹窗 */}
            <Modal
                title={isEditMode ? "编辑媒体库" : "创建媒体库"}
                className={styles.modal}
                closeIcon={<PreloadImage style={{width: '20px', height: '20px'}} src={isDarkMode ? close_white:close } alt="关闭" />}
                open={visible}
                onCancel={handleCancel}
                footer={null}
                width={600}
                centered
                destroyOnClose={true}
                maskClosable={false}
            >
                <div className={styles.modalContainer}>
                    {/* 可滚动的表单内容区域 */}
                    <div className={styles.scrollableFormContent}>
                        <Form
                            form={form}
                            style={{ padding: '20px 0' }}
                            preserve={false}
                        >
                    {/* 媒体库名称 */}
                    <Form.Item
                        name="libraryName"
                        className={styles.libraryNameInput}
                    >
                        <Input
                            placeholder="请输入媒体库名称"
                            style={{ color: 'var(--text-color)', backgroundColor: 'var(--library-switch-bg)', height: '54px' ,borderRadius:'18px',border:'none'}}
                            onFocus={handleInputFocus}
                            onChange={handleInputChange}
                            maxLength={10}
                        />
                    </Form.Item>

                    {/* 电视端显示开关 */}
                    <Form.Item name="showOnTV" valuePropName="checked">
                        <div className={styles.switch}>
                            <span>允许当前媒体库在电视端显示</span>
                            <Switch  onChange={(value) => {
                                form.setFieldsValue({
                                    showOnTV: value
                                });
                            }} />
                        </div>
                    </Form.Item>
                    <Divider />
                    {/* 来源区域 */}
                    <div style={{ marginBottom: '20px' }}>
                        <div style={{ marginBottom: '10px', color: 'rgba(140, 147, 176, 1)' }}>来源</div>
                        <div className={styles.createLibraryButton} onClick={handleAddSource}>
                            <span>添加媒体库来源</span>
                            <PreloadImage src={add_icon || ''} alt='add_icon' style={{ width: '24px', height: '24px' }} />
                        </div>
                        {/* 显示已选择的来源 */}
                        {selectedSources.map((item, index) => (
                            <div key={index} className={styles.selectedSource}>
                                <span>{item.displayPath}</span>
                                <PreloadImage src={del_icon_2} style={{ width: '24px', height: '24px' }} alt="删除" onClick={() => handleRemoveSource(index)} />
                            </div>
                        ))}
                    </div>
                    <Divider />
                    {/* 访问权限区域 */}
                    <div style={{ marginBottom: '20px' }}>
                        <div style={{ marginBottom: '10px', color: 'rgba(140, 147, 176, 1)' }}>允许访问媒体库</div>
                        <div className={styles.createLibraryButton} onClick={handleAddUser}>
                            <span>添加共享用户</span>
                            <PreloadImage src={add_icon || ''} alt='add_icon' style={{ width: '24px', height: '24px' }} />
                        </div>
                        {/* 显示已选择的用户 */}
                        {selectedUsers.map((user) => (
                            <div key={user.id} className={styles.selectedUser}>
                                <div className={styles.userInfo}>
                                    <Avatar
                                        size={50}
                                        src={user.avatar}
                                        icon={<UserOutlined />}
                                        className={styles.avatar}
                                    />
                                    <div className={styles.userDetails}>
                                        <div className={styles.userName}>{user.name}</div>
                                        <div className={styles.userPosition}>{user.position}</div>
                                    </div>
                                </div>
                                <PreloadImage src={del_icon} alt='minus' onClick={() => handleRemoveUser(user.id)} />
                            </div>
                        ))}
                    </div>
                        </Form>
                    </div>

                    {/* 固定底部的保存按钮 */}
                    <div className={styles.fixedFooter}>
                        <Button
                            onClick={() => {
                                form.validateFields().then(values => {
                                    handleSubmit(values);
                                }).catch(err => {
                                    console.log('表单验证失败:', err);
                                });
                            }}
                            className={styles.commitBtn}
                            loading={loading}
                            disabled={loading || !canSubmit}
                        >
                            {loading ? (isEditMode ? '保存中...' : '创建中...') : (isEditMode ? '保存' : '保存')}
                        </Button>
                    </div>
                </div>
            </Modal>

            {/* 文件选择器 */}
            <FileSelector
                visible={isFileSelectorOpen}
                onClose={handleFileSelectorClose}
                onSelect={handleSourceSelect}
                title="选择来源"
            />

            {/* 用户选择器 */}
            <UserSelector
                visible={isUserSelectorOpen}
                onClose={handleUserSelectorClose}
                onConfirm={handleUserConfirm}
                selectedUserIds={selectedUsers.map(user => user.id)}
                title="添加用户"
            />
        </>
    );
};

export default CreateLibraryModal; 