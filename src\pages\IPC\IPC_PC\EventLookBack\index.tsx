import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import dataSelect from '@/Resources/player/dateSelect.png';
import { ConfigProvider, DatePicker, Divider, Table, TableProps } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { endOfDay, format, startOfDay } from 'date-fns';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import type { Dayjs } from 'dayjs';
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import hoverImage from "@/Resources/file/hoverImage.png";
import nullData from "@/Resources/camMgmtImg/null-page.png";
import nullDataDark from "@/Resources/camMgmtImg/null-page-dark.png";
import PopoverSelector from '@/components/PopoverSelector';
import { callTaskCenter, downloadAndSaveWithPlayer } from '@/api/cameraPlayer';
import { Toast } from '@/components/Toast/manager';
import LookBackMovieList from './LookBackMovieList';
import { cameraIconInfo, eventDefinition, EventLookBackType } from '@/components/CameraPlayer/constants';
import { ICameraDetail } from '../../IPC_APP/CameraDetail';
import { ICollapsePanel } from '@/layouts/Layout';
import { delRecordVideo, getVideoRecord } from '@/api/ipc';
import { useInViewport, useUpdateEffect } from 'ahooks';
import { useCameras } from '..';
import { useTheme } from '@/utils/themeDetector';

dayjs.locale('zh-cn');

export interface eventLookBackData {
  date: string;
  data: eventLookBackDataType[]
}

export interface eventLookBackDataType {
  id: number;
  eventTime: string
  eventName: string;
  deviceName: string;
  url: string;
  movieUrl: string
}

const TableComponent = (props: { data: eventLookBackDataType[], date: string, selects: any[], setSelects: (v: any) => void, setModalIsShow: (v: any) => void, setCurData: (v: any) => void }) => {
  const { data, date, setSelects, selects, setModalIsShow, setCurData } = props;
  const [isHoverKey, setIsHoverKey] = useState<number>(-1);
  const selectHandler = useCallback((rcd: eventLookBackDataType) => {
    setSelects((prev: any) => {
      if (prev.includes(rcd)) {
        return prev.filter((item: any) => item.id !== rcd.id);
      }
      return [...prev, rcd];
    })
  }, [setSelects])

  // 双击事件处理函数
  const handleDoubleClick = useCallback((record) => {
    setModalIsShow(true);
    setCurData(record);
  }, [setCurData, setModalIsShow])

  // 单击播放处理函数
  const handleClickPlay = useCallback((record) => {
    setModalIsShow(true);
    setCurData(record);
  }, [setCurData, setModalIsShow])

  const columns: TableProps<eventLookBackDataType>['columns'] = useMemo(() => {
    return [
      { title: date, dataIndex: 'eventTime', key: 'eventTime', minWidth: 100 },
      { title: '事件', dataIndex: 'eventName', key: 'eventName', minWidth: 100 },
      { title: '设备', dataIndex: 'deviceName', key: 'deviceName' },
      {
        title: '视频', dataIndex: 'url', key: 'url', render: (_, record) => (
          <div className={styles.urlContainer}>
            <div className={styles.tableImage} onDoubleClick={() => handleDoubleClick(record)}>
              <PreloadImage src={record.url} alt='tableImage' />
            </div>
            <div className={styles.hoverImage} style={{ display: isHoverKey === record.id ? 'flex' : 'none' }} onClick={() => handleClickPlay(record)}>
              <PreloadImage src={hoverImage} alt='hoverImage' />
            </div>
          </div>
        ), width: 100
      },
      {
        title: '', dataIndex: '', key: '', render: (_, record) => (
          <div className={styles.selectIcon} style={{ visibility: selects.includes(record) || isHoverKey === record.id ? 'visible' : 'hidden' }} onClick={() => selectHandler(record)}>
            <PreloadImage src={selects.includes(record) ? selected : notSelect} alt='select' />
          </div>
        ), width: 30
      },
    ]
  }, [date, handleClickPlay, handleDoubleClick, isHoverKey, selectHandler, selects])

  return <Table rowKey={'id'} tableLayout="auto" columns={columns} dataSource={data} bordered={false} pagination={false} onRow={(record) => {
    return {
      onMouseEnter: () => setIsHoverKey(record.id),
      onBlur: () => setIsHoverKey(-1),
      onMouseLeave: () => setIsHoverKey(-1),
    }
  }} />
}
const defaultPageOptions = { size: 20, token: '' }
const IPCDesktopEventLookBack = () => {
  const now = new Date();
  const [dates, setDates] = useState<[start: Dayjs | null, end: Dayjs | null]>([dayjs(now), dayjs(now)]);
  const [eventData, setEventData] = useState<eventLookBackData[]>([]);
  const [curData, setCurData] = useState<eventLookBackDataType | undefined>();
  const [modalIsShow, setModalIsShow] = useState<boolean>(false);
  const [selects, setSelects] = useState<eventLookBackDataType[]>([]);
  const [deviceIsShowSelector, setDeviceIsShowSelector] = useState<boolean>(false);
  const [eventIsShowSelector, setEventIsShowSelector] = useState<boolean>(false);
  const [curDeviceKey, setCurDeviceKey] = useState<string>('all');
  const [curEvent, setCurEvent] = useState<any>(null);
  const pageRef = useRef<{ size: number, token: string }>(defaultPageOptions);

  const timeRef = useRef<{ start: number, end: number }>({ start: startOfDay(now).getTime(), end: endOfDay(now).getTime() });
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const { isDarkMode } = useTheme();

  const { cameras } = useCameras(); // 已录制摄像机操作对象
  const [deviceDetail, setDeviceDetail] = useState<(ICollapsePanel & ICameraDetail)[]>([]); // 摄像机操作对象列表

  useEffect(() => {
    if (cameras.length > 0) {
      setDeviceDetail(cameras.map(it => ({ ...it, label: it.model, key: it.did, name: it.name, icon: cameraIconInfo(it.model) })));
    }
  }, [cameras])

  // 当前设备
  const curDevice: (ICollapsePanel & ICameraDetail) | undefined = useMemo(() => {
    return deviceDetail.find((item) => item.key === curDeviceKey);
  }, [deviceDetail, curDeviceKey])

  const camera_lens = useMemo(() => {
    if (!curDevice) return [];
    const temp: string[] = [];
    if (curDeviceKey === 'all') {
      deviceDetail.forEach((it) => { it.key_frame.forEach((item) => temp.push(`${it.did}_${item.lens_id}`)) })
    } else {
      curDevice.key_frame.forEach((it) => {
        temp.push(`${curDevice.did}_${it.lens_id}`);
      })
    }
    return temp
  }, [curDevice, curDeviceKey, deviceDetail])

  const getData = useCallback(async (deviceDetail: (ICollapsePanel & ICameraDetail)[], callback: (temp: eventLookBackData[]) => void) => {
    const t: EventLookBackType[] = curEvent === 'all' ? ['human', 'fire', 'pet'] : [curEvent];

    const params = {
      page: pageRef.current,
      options: {
        option: ['event_name', 'time', 'camera_lens'],
        event_name: t,
        camera_lens: camera_lens,
        time: {
          start: timeRef.current.start,
          end: timeRef.current.end
        }
      }
    }

    const res = await getVideoRecord(params).then().catch(() => null);
    if (res && res.code === 0 && res.data) {

      if (res.data.videos.length === pageRef.current.size) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }

      let obj: { [key: string]: eventLookBackDataType[] } = {};
      const tempVideos = res.data.videos.filter((it) => it.event_name !== '');
      for (const it of tempVideos) {
        const date = new Date(Number(it.time));
        const day: string = format(date, 'MM月dd日');
        const time: string = format(date, 'HH:mm');
        const device = deviceDetail.find(item => item.did === it.camera_lens.split('_')[0]);
        const id = tempVideos.indexOf(it);

        const tempObj = {
          id: id,
          eventTime: time,
          eventName: eventDefinition[it.event_name].label,
          deviceName: device ? device.model : '',
          url: `${it.cover_file}/original`,
          movieUrl: it.file
        }

        // 如果已有数据，则追加到已有数据的后面
        if (obj[day]) {
          obj[day].push(tempObj)
        } else {
          obj[day] = [tempObj]
        }
      }
      const temp: eventLookBackData[] = Object.keys(obj).map((key) => ({ date: key, data: obj[key] }));
      callback(temp);
      pageRef.current = { ...pageRef.current, token: res.data.page.token }
    }

    if (res && res.code === 5000) {
      Toast.show('请求超时，请稍后再试');
    }
  }, [camera_lens, curEvent])

  // 日期改变
  useUpdateEffect(() => {
    if (dates) {
      timeRef.current = { start: startOfDay(dates[0]!.toDate()).getTime(), end: endOfDay(dates[1]!.toDate()).getTime() };
      pageRef.current = defaultPageOptions;
      if (deviceDetail.length > 0) {
        getData(deviceDetail, (temp) => {
          setEventData(temp);
        });
      }
    }
  }, [dates, getData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport && deviceDetail.length > 0) {
      getData(deviceDetail, (temp) => {
        setEventData((prev) => {
          const p = [...prev];
          const tempData: eventLookBackData[] = [];
          const keys = p.map(item => item.date);
          for (const it of p) {
            const newData = temp.find(item => item.date === it.date);
            if (newData) {
              tempData.push({ date: it.date, data: [...it.data, ...newData.data] });
            }
          }
          for (const it of temp) {
            // 如果当前日期不存在，则直接添加
            if (!keys.includes(it.date)) {
              tempData.push(it);
            }
          }
          return tempData;
        });
      });
    }
  }, [inViewport, getData, deviceDetail]);

  const onDelete = useCallback(async () => {
    const res = await delRecordVideo({ videos: selects.map((item) => item.movieUrl) }).catch((e) => {
      Toast.show('删除失败，请稍后再试');
    });
    if (res && res.code === 0) {
      Toast.show('正在删除');
      setSelects([]);
      return;
    }

    if (res && res.code === 5000) {
      Toast.show('请求超时，请稍后再试');
      setSelects([]);
    }
  }, [selects])

  const onDownload = useCallback(async () => {
    const urls = selects.map((item) => item.movieUrl);
    await downloadAndSaveWithPlayer(urls, (res) => {
      if (res && res.code === 0) {
        Toast.show('正在下载');
      }
    })

    await callTaskCenter();
  }, [selects])

  const deviceSelectOptions = useMemo(() => {
    const ops = deviceDetail.map((item) => {
      return {
        label: item.name,
        value: item.key,
        icon: item.icon,
        subtitle: item.model
      }
    })
    ops.unshift({ label: '全部设备', value: 'all', icon: '', subtitle: '' });
    return ops
  }, [deviceDetail])

  const eventSelectOptions = useMemo(() => {
    return Object.keys(eventDefinition).map((item) => {
      return {
        label: eventDefinition[item].label,
        value: item,
        icon: eventDefinition[item].icon
      }
    })
  }, [])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.operation}>
          {
            selects.length > 0 ?
              <>
                <div className={styles.operationItem} onClick={onDownload}>
                  <span>下载</span>
                </div>
                <div className={styles.operationItem} style={{ color: 'red' }} onClick={onDelete}>
                  <span>删除</span>
                </div>
              </> : <>
                <PopoverSelector visible={deviceIsShowSelector} onVisibleChange={setDeviceIsShowSelector} onChange={(value) => {
                  setCurDeviceKey(value);
                  pageRef.current = defaultPageOptions // 重置分页信息
                }} options={deviceSelectOptions} value={curDevice ? curDevice.key : 'all'}>
                  <div className={styles.operationItem}>
                    <span>{curDevice ? curDevice.name : '全部设备'}</span>
                    <PreloadImage src={dataSelect} alt='select' />
                  </div>
                </PopoverSelector>
                <div className={styles.operationItem_datePicker}>
                  <ConfigProvider locale={locale}>
                    <DatePicker.RangePicker disabledDate={(d) => d > dayjs().endOf('day')} defaultValue={dates} onChange={(dates, dateString) => { dates && setDates(dates) }} />
                  </ConfigProvider>
                </div>
                <PopoverSelector visible={eventIsShowSelector} onVisibleChange={setEventIsShowSelector} onChange={(value) => {
                  setCurEvent(value);
                  pageRef.current = defaultPageOptions // 重置分页信息
                }} options={eventSelectOptions} value={curEvent}>
                  <div className={styles.operationItem}>
                    <span>{eventDefinition[curEvent]?.label}</span>
                    <PreloadImage src={dataSelect} alt='select' />
                  </div>
                </PopoverSelector>
              </>
          }
        </div>
      </div>
      <div className={styles.content}>
        {
          eventData.length > 0 ? eventData.map((item, index) => (
            <div key={item.date}>
              <TableComponent selects={selects} setSelects={setSelects} data={item.data} date={item.date} setCurData={setCurData} setModalIsShow={setModalIsShow} />
              {index === eventData.length - 1 ? <></> : <Divider />}
            </div>
          )) : <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>
              <PreloadImage
                src={isDarkMode ? nullDataDark : nullData}
                alt="暂无数据"
              />
            </div>
            <div className={styles.emptyText}>暂无数据</div>
          </div>
        }
        {hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)}
      </div>
      <LookBackMovieList data={eventData} curData={curData} isShow={modalIsShow} setIsShow={setModalIsShow} />
    </div>
  )
}

export default IPCDesktopEventLookBack;