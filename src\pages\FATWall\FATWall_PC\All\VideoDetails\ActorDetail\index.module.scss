.container {
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #fff;
  height: 100vh;
  overflow: hidden;
}

.headerSection {
  position: relative;
  height: 300px;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.backButton {
  position: relative;
  z-index: 2;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-start;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  svg {
    color: #fff;
    font-size: 18px;
  }
}

.actorInfo {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-end;
  height: 100px;
}

.actorName {
  font-size: 48px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.2;
}

.worksSection {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background-color: #1a1a1a;

  // 设置滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.sectionTitle {
  font-family: 'MiSans W', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.2;
  color: #fff;
  margin: 0 0 24px 0;
}

.worksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 20px;
  width: 100%;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.emptyText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
}